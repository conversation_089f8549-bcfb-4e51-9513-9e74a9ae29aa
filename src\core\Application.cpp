#include "core/Application.hpp"
#include "controllers/ApiController.hpp"
#include "controllers/StaticController.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/SyncStatusDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "services/SyncService.hpp"
#include <spdlog/spdlog.h>

Application::Application(const std::string &config_file)
    : config_file_(config_file)
    , running_(false)
{
}

Application::~Application()
{
    stop();
    database_manager_.close();
}

bool Application::initialize()
{
    SPDLOG_INFO("Initializing Daily Report System...");

    {
        std::ifstream configFile("config.json");
        config_ = nlohmann::json::parse(configFile);
    }

    if (!database_manager_.initialize(config_.database.path))
    {
        SPDLOG_ERROR("Failed to initialize database");
        return false;
    }

    // Initialize DAO components after database is ready
    initializeDaos();

    SPDLOG_INFO("Application initialized successfully");
    return true;
}

void Application::start()
{
    if (initialize())
    {
        api_ctrl_ = std::make_shared<ApiController>(*this);
        file_ctrl_ = std::make_shared<StaticController>(*this);
        sync_service_ = std::make_shared<SyncService>(*this);

        startServer();
    }
}

void Application::stop()
{
    if (running_)
    {
        running_ = false;
        server_.stop();
        if (server_thread_.joinable())
        {
            server_thread_.join();
        }
        SPDLOG_INFO("Server stopped");
    }
}

void Application::startServer()
{
    running_ = true;
    SPDLOG_INFO("Starting server on {}:{}", config_.server.host, config_.server.port);

    server_thread_ = std::thread([this]() {
        if (!server_.listen(config_.server.host, config_.server.port))
        {
            SPDLOG_ERROR("Failed to start server on {}:{}", config_.server.host, config_.server.port);
            running_ = false;
        }
    });

    SPDLOG_INFO("Server started successfully");

    // Wait for server thread to complete
    if (server_thread_.joinable())
    {
        server_thread_.join();
    }
}

void Application::initializeDaos()
{
    product_dao_ = std::make_shared<ProductDao>(database_manager_);
    project_dao_ = std::make_shared<ProjectDao>(database_manager_);
    customer_dao_ = std::make_shared<CustomerDao>(database_manager_);
    user_dao_ = std::make_shared<UserDao>(database_manager_);
    work_hour_dao_ = std::make_shared<WorkHourDao>(database_manager_);
    holiday_dao_ = std::make_shared<HolidayDao>(database_manager_);
    sync_status_dao_ = std::make_shared<SyncStatusDao>(database_manager_);
}
