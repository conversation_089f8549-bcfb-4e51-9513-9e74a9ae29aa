# 开发文档

## 技术栈

### 后端技术栈

#### 核心框架
- **HTTP 服务器**: oatpp 1.3.0+
  - 高性能 C++ Web 框架
  - 支持异步 I/O 和多线程
  - 内置 JSON 序列化/反序列化
  - RESTful API 支持

#### 数据库技术
- **数据库**: SQLite3
  - 轻量级嵌入式数据库
  - 无需独立服务器进程
  - 支持 ACID 事务
  - 文件存储，便于部署
- **数据库访问**: SQLiteCpp
  - C++ SQLite 包装库
  - 类型安全的参数绑定
  - RAII 资源管理
  - 异常安全

#### HTTP 客户端
- **HTTP 库**: libcurl
  - 成熟稳定的 HTTP/HTTPS 客户端库
  - 支持多种协议
  - 连接池和重用
  - 超时和重试机制

#### 日志系统
- **日志库**: spdlog
  - 高性能 C++ 日志库
  - 多种输出格式支持
  - 异步日志写入
  - 日志级别控制

#### JSON 处理
- **JSON 库**: nlohmann/json
  - 现代 C++ JSON 库
  - 直观的 API 设计
  - 自动类型转换
  - 宏定义序列化支持

#### 构建系统
- **构建工具**: CMake 3.16+
  - 跨平台构建系统
  - 依赖管理
  - 预设配置支持
- **包管理**: vcpkg
  - Microsoft C++ 包管理器
  - 自动依赖解析
  - 跨平台支持

### 前端技术栈

#### 核心技术
- **HTML5**: 语义化标记，现代 Web 标准
- **CSS3**: 
  - Flexbox 布局
  - CSS Grid（未来扩展）
  - 渐变和阴影效果
  - 过渡动画和变换
  - 响应式设计
- **JavaScript ES6+**:
  - 原生 JavaScript，无框架依赖
  - Async/Await 异步编程
  - 模块化代码组织
  - DOM 操作和事件处理

#### 设计特点
- **单页应用 (SPA)**: 无页面刷新的用户体验
- **响应式设计**: 适配不同屏幕尺寸
- **现代化 UI**: 扁平化设计，微交互动画
- **无框架**: 轻量级，快速加载

## 系统架构

### 整体架构

```
┌─────────────────┐    HTTP/JSON     ┌─────────────────┐    HTTP/JSON     ┌─────────────────┐
│   Web Browser   │ ◄──────────────► │  Local Server   │ ◄──────────────► │ Remote Backend  │
│                 │                  │     (C++)       │                  │  (192.168.100.  │
│ HTML/CSS/JS     │                  │                 │                  │     236:7000)   │
└─────────────────┘                  └─────────────────┘                  └─────────────────┘
                                              │
                                              ▼
                                     ┌─────────────────┐
                                     │   SQLite DB     │
                                     │ (dailyreport.db)│
                                     └─────────────────┘
```

### 模块架构

#### 1. HTTP 服务层
```cpp
// 主要组件
- ApiController: RESTful API 端点
- StaticController: 静态文件服务
- ErrorHandler: 错误处理中间件
- CORS: 跨域请求处理
```

#### 2. 业务逻辑层
```cpp
// 服务组件
- SyncService: 数据同步服务
- HttpClient: HTTP 客户端封装
- ConfigManager: 配置管理
- DatabaseManager: 数据库连接管理
```

#### 3. 数据访问层
```cpp
// DAO 组件
- ProductDao: 产品数据访问
- ProjectDao: 项目数据访问  
- CustomerDao: 客户数据访问
- SyncStatusDao: 同步状态数据访问
```

#### 4. 数据模型层
```cpp
// 模型对象
- Product: 产品实体
- Project: 项目实体
- Customer: 客户实体
- SyncStatus: 同步状态实体
- WorkHour: 工时实体（待实现）
```

### 目录结构

```
DailyReport2/
├── include/                    # 头文件目录
│   ├── controllers/           # 控制器
│   │   ├── ApiController.hpp
│   │   └── StaticController.hpp
│   ├── services/              # 服务层
│   │   ├── SyncService.hpp
│   │   └── HttpClient.hpp
│   ├── dao/                   # 数据访问层
│   │   ├── ProductDao.hpp
│   │   ├── ProjectDao.hpp
│   │   ├── CustomerDao.hpp
│   │   └── SyncStatusDao.hpp
│   ├── models/                # 数据模型
│   │   ├── Product.hpp
│   │   ├── Project.hpp
│   │   ├── Customer.hpp
│   │   ├── SyncStatus.hpp
│   │   └── WorkHour.hpp
│   ├── database/              # 数据库管理
│   │   └── DatabaseManager.hpp
│   ├── config/                # 配置管理
│   │   └── Config.hpp
│   └── http/                  # HTTP 客户端
│       └── HttpClient.hpp
├── src/                       # 源文件目录
│   └── main.cpp              # 程序入口
├── web/                       # 前端资源
│   ├── index.html            # 主页面
│   ├── css/                  # 样式文件（可选）
│   └── js/                   # JavaScript 文件（可选）
├── config/                    # 配置文件
│   └── config.json
├── data/                      # 数据目录
│   └── dailyreport.db        # SQLite 数据库
├── doc/                       # 文档目录
│   ├── req.md                # 需求文档
│   ├── dev.md                # 开发文档
│   └── api.md                # API 文档
├── CMakeLists.txt            # CMake 构建文件
├── CMakePresets.json         # CMake 预设配置
├── vcpkg.json               # vcpkg 依赖配置
└── build.bat                # Windows 构建脚本
```

## 数据流

### 数据同步流程

```
1. 定时器触发 / 手动触发
   ↓
2. SyncService::syncAll()
   ↓
3. HttpClient 调用远程 API
   ↓
4. JSON 数据解析为 C++ 对象
   ↓
5. DAO 层写入 SQLite 数据库
   ↓
6. 更新 sync_status 表
   ↓
7. 前端轮询获取最新状态
```

### Web 请求流程

```
1. 浏览器发送 HTTP 请求
   ↓
2. oatpp 路由到对应 Controller
   ↓
3. Controller 调用 Service 层
   ↓
4. Service 调用 DAO 层
   ↓
5. DAO 查询 SQLite 数据库
   ↓
6. 数据序列化为 JSON
   ↓
7. HTTP 响应返回给浏览器
```

## 开发环境

### 必需工具
- **编译器**: 
  - Windows: Visual Studio 2019+ 或 MSVC 14.2+
  - Linux: GCC 9+ 或 Clang 10+
  - macOS: Xcode 12+ 或 Clang 12+
- **CMake**: 3.16 或更高版本
- **vcpkg**: 最新版本
- **Git**: 版本控制

### 依赖库版本
```json
{
  "dependencies": {
    "oatpp": "1.3.0",
    "sqlitecpp": "3.2.1", 
    "nlohmann-json": "3.11.2",
    "spdlog": "1.11.0",
    "curl": "8.4.0"
  }
}
```

### 构建命令
```bash
# 配置构建
cmake --preset msvc-debug

# 编译
cmake --build --preset msvc-debug

# 或使用批处理脚本（Windows）
./build.bat
```

## 性能特点

### 后端性能
- **并发处理**: oatpp 多线程模型，支持高并发请求
- **内存管理**: RAII 模式，自动资源管理
- **数据库**: SQLite WAL 模式，提升并发读写性能
- **HTTP 客户端**: 连接复用，减少网络开销

### 前端性能
- **轻量级**: 无框架依赖，快速加载
- **缓存策略**: 静态资源缓存
- **异步加载**: 数据按需加载，避免阻塞
- **DOM 优化**: 最小化 DOM 操作，提升渲染性能

## 扩展性设计

### 水平扩展
- **数据库**: 可迁移到 PostgreSQL/MySQL 支持更大数据量
- **缓存**: 可集成 Redis 提升查询性能
- **负载均衡**: 支持多实例部署

### 功能扩展
- **插件系统**: 模块化设计，便于功能扩展
- **API 版本**: 支持 API 版本管理
- **认证授权**: 可集成 JWT/OAuth2
- **实时通信**: 可添加 WebSocket 支持
