#pragma once

#include "JsonUtil.hpp"
#include <nlohmann/json.hpp>
#include <string>

struct Project
{
    int id = 0;
    std::string project_code;
    std::string project_name;
    std::string short_name;
    int64_t start_time;
    int64_t end_time;
    int64_t plan_start_time;
    int64_t plan_end_time;
    std::string duty_persons;
    std::string duty_departments;
    std::string join_departments;
    std::string join_persons;
    std::string project_type;
    std::string project_state;
    std::string rel_product_list;
    int rel_customer_info = 0;
    std::string project_comment;
    std::string contract_name;
};

void from_json(const nlohmann::json &j, Project &p)
{
    j["id"].get_to(p.id);
    j["projectCode"].get_to(p.project_code);
    j["projectName"].get_to(p.project_name);
    p.short_name = JsonUtil::get<std::string>(j, "shortName", "");
    p.duty_persons = JsonUtil::get<std::string>(j, "dutyPersons", "");
    p.duty_departments = JsonUtil::get<std::string>(j, "dutyDepartments", "");
    p.join_departments = JsonUtil::get<std::string>(j, "joinDepartments", "");
    p.project_type = JsonUtil::get<std::string>(j, "projectType", "");
    p.project_state = JsonUtil::get<std::string>(j, "projectState", "");
    p.project_comment = JsonUtil::get<std::string>(j, "projectComment", "");
    p.contract_name = JsonUtil::get<std::string>(j, "contractName", "");
    p.join_persons = JsonUtil::get<std::string>(j, "joinPersons");
    p.rel_customer_info = JsonUtil::get<int>(j, "relCustomerInfo", -1);
    p.rel_product_list = JsonUtil::value<std::vector<int>>(j, "relProductList").dump();
    p.start_time = JsonUtil::get<int64_t>(j, "startTime", 0);
    p.end_time = JsonUtil::get<int64_t>(j, "endTime", 0);
    p.plan_start_time = JsonUtil::get<int64_t>(j, "planStartTime", 0);
    p.plan_end_time = JsonUtil::get<int64_t>(j, "planEndTime", 0);
}

void to_json(nlohmann::json &j, const Project &p)
{
    j = nlohmann::json{
        { "id", p.id },
        { "project_code", p.project_code },
        { "project_name", p.project_name },
        { "short_name", p.short_name },
        { "start_time", p.start_time },
        { "end_time", p.end_time },
        { "plan_start_time", p.plan_start_time },
        { "plan_end_time", p.plan_end_time },
        { "duty_persons", p.duty_persons },
        { "duty_departments", p.duty_departments },
        { "join_departments", p.join_departments },
        { "join_persons", p.join_persons },
        { "project_type", p.project_type },
        { "project_state", p.project_state },
        { "rel_product_list", p.rel_product_list },
        { "rel_customer_info", p.rel_customer_info },
        { "project_comment", p.project_comment },
        { "contract_name", p.contract_name },
    };
}
