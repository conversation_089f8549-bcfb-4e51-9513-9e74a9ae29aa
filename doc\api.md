﻿# API 文档

## 配置
- 接口地址: http://192.168.100.236:7000/atom/v1/workhour/api

## 更新 token
- 方法: post
- 地址: ${api_url}/atomRbacService/oauth/refreshToken?refreshToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************.Z3HG7LgsOGMpFn5CVmX1WGZiXAIzkygTqgmPoFgbYdA
- 结果示例:
```json
{
    "code": 200,
    "msg": "成功",
    "data": {
        "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************.P_gNZgWb--GeiVIQzz3A1JtXd6z1xPXLP_6eiswqI1Y",
        "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************.6NnDnPix5oivvl-RiwmT4YTnoJ2Ls-gNuM8y5WgEWVI",
        "tokenName": "Authorization",
        "expiresIn": 86400,
        "refreshExpiresIn": 1036800,
        "userInfo": {
            "id": "626782691571802528",
            "account": "gx",
            "name": "郭轩",
            "gender": null,
            "mobile": null,
            "email": null,
            "phone": null,
            "post": null,
            "type": null,
            "enable": true,
            "superFlag": false,
            "adminFlag": false
        }
    }
}
```

## 获取 Product
- 方法: get
- 地址: ${api_url}/product/list
- 结果示例:
```json
{
    "code": 0,
    "msg": "ok",
    "data": [
        {
            "id": 10,
            "projectCode": "*********",
            "projectName": "任务管控系统（运控）",
            "shortName": "任务管控系统（运控）",
            "startTime": 0E-9,
            "endTime": 0E-9,
            "planStartTime": 0E-9,
            "planEndTime": 0E-9,
            "dutyPersons": "626782694092579232",
            "dutyDepartments": "8",
            "joinDepartments": "",
            "joinPersons": "626782694092579232,626782694264545696,626782691731186080,***************248,626782694344237472,626782694302294432",
            "projectType": "CONTRACT_OUT",
            "projectState": "OP",
            "relProductList": [
                12
            ],
            "relCustomerInfo": 7,
            "projectComment": "",
            "contractName": "任务管控系统（运控）",
            "projectTime": null
        }
    ]
}
```

## 获取 Project
- 方法: get
- 地址: ${api_url}/project/list
- 结果示例:
```json
{
    "code": 0,
    "msg": "ok",
    "data": [
        {
            "id": 10,
            "projectCode": "*********",
            "projectName": "任务管控系统（运控）",
            "shortName": "任务管控系统（运控）",
            "startTime": 0E-9,
            "endTime": 0E-9,
            "planStartTime": 0E-9,
            "planEndTime": 0E-9,
            "dutyPersons": "626782694092579232",
            "dutyDepartments": "8",
            "joinDepartments": "",
            "joinPersons": "626782694092579232,626782694264545696,626782691731186080,***************248,626782694344237472,626782694302294432",
            "projectType": "CONTRACT_OUT",
            "projectState": "OP",
            "relProductList": [
                12
            ],
            "relCustomerInfo": 7,
            "projectComment": "",
            "contractName": "任务管控系统（运控）",
            "projectTime": null
        }
    ]
}
```


## 获取 Customer
- 方法: get
- 地址: ${api_url}/customer/list
- 结果示例:
```json
{
    "code": 0,
    "msg": "ok",
    "data": [
        {
            "id": 7,
            "unitName": "509",
            "contact": null,
            "department": null,
            "phones": null,
            "groupName": "CASC"
        },
        {
            "id": 8,
            "unitName": "805",
            "contact": null,
            "department": null,
            "phones": null,
            "groupName": "CASC"
        }
    ]
}
```

## 获取 User
- 方法: get
- 地址: ${api_url}/atom/v1/atomRbacService/user/listPage

- 结果示例:
```json
{
    "code": 200,
    "msg": "成功",
    "data": {
        "pageNo": 0,
        "pageSize": 50,
        "totalPage": 1,
        "total": 11,
        "list": [
            {
                "id": "699565625978136608",
                "account": "hpx",
                "name": "何攀雄",
                "gender": null,
                "mobile": null,
                "email": null,
                "phone": null,
                "post": null,
                "type": null,
                "enable": true,
                "deptName": "研发1组",
                "teamName": null,
                "roleName": "开发人员"
            },
        ]
    }
}
```

## 获取 WorkHour
- 方法: get
- 地址: ${api_url}/atom/v1/workhour/api/task/listMySubmitTaskWorkHour?startTime=*************&endTime=*************
- 参数: startTime 起始时间
        endTime   结束时间
- 结果示例:
```json
{
    "code": 0,
    "msg": "ok",
    "data": {
        "pageNo": 0,
        "pageSize": 50,
        "totalPage": 1,
        "total": 31,
        "list": [
            {
                "id": 3547,
                "createTime": *************.*********,
                "updateTime": *************.*********,
                "deleted": 0,
                "submitDate": 1749128082902,
                "employeeId": 626782691773129120,
                "employeeName": "李刚刚",
                "needCoordinateWork": "",
                "tomorrowPlan": "继续修改参数关联逻辑，实现PPK参数配置。",
                "timeConsumedList": [
                    {
                        "id": 6822,
                        "createTime": 1749128082937.*********,
                        "updateTime": 1749128082937.*********,
                        "deleted": 0,
                        "workHourId": 3547,
                        "timeStart": null,
                        "timeEnd": null,
                        "submitWorkHour": 480,
                        "value": [
                            "9:30",
                            "10:00",
                            "10:30",
                            "11:00",
                            "11:30",
                            "12:00",
                            "13:30",
                            "14:00",
                            "14:30",
                            "15:00",
                            "15:30",
                            "16:00",
                            "16:30",
                            "17:00",
                            "17:30",
                            "18:00"
                        ],
                        "deptList": [
                            7
                        ],
                        "projectId": 112,
                        "type": 2,
                        "submitComment": "测试下发参数配置数据和客户旧软件不一致，对比参数文档修改参数关联问题。",
                        "employeeId": 626782691773129120,
                        "employeeName": "李刚刚"
                    }
                ]
            }
        ]
    }
}
``` 



## 提交 WorkHour
- 方法: post
- 地址: ${api_url}/atom/v1/workhour/api/task/submitWorkHour
- 参数: 
```json
{"createTime": 1746009476356, "updateTime": 1746009476356, "submitDate": 1746009476023, "employeeId": "626782691571802528", "employeeName": "郭轩", 
"timeConsumedList": [{"submitComment": "参加611综合电子模拟器设计沟通会议", "projectId": "", "type": 0, "value": ["9:30", "10:00", "10:30", "11:00"], "submi
tWorkHour": 120, "createTime": 1746009343382, "updateTime": 1746009343382}, {"submitComment": "测试 AtsClient v3.0.0b2; 测试 FTS v4.4.0b6", "projectId": "",
 "type": 0, "value": ["11:30", "12:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00"], "submitWorkHour": 300, "createTime": 174601
0395690, "updateTime": 1746010395690}, {"submitComment": "整理周会材料，排下周计划", "projectId": "", "type": 0, "value": ["17:30", "18:00"], "submitWorkHou
r": 60, "createTime": 1746010606445, "updateTime": 1746010606445}], "tomorrowPlan": "", "needCoordinateWork": "" }
```
- 结果示例:
```json
```


## 同步放假日数据
- 方法: get
- 地址: https://raw.githubusercontent.com/NateScarlet/holiday-cn/master/${year}.json
- 参数: 无
- 说明: 在软件启动时检查放假日数据是否已同步到本地，如果没有则从2024年循环到(本年+1)同步到本地数据库，重试3次后仍失败则提示错误并退出
- 结果示例:
```json
{
    "$schema": "https://raw.githubusercontent.com/NateScarlet/holiday-cn/master/schema.json",
    "$id": "https://raw.githubusercontent.com/NateScarlet/holiday-cn/master/2025.json",
    "year": 2025,
    "papers": [
        "https://www.gov.cn/zhengce/zhengceku/202411/content_6986383.htm"
    ],
    "days": [
        {
            "name": "元旦",
            "date": "2025-01-01",
            "isOffDay": true
        },
        {
            "name": "春节",
            "date": "2025-01-26",
            "isOffDay": false
        },
        {
            "name": "春节",
            "date": "2025-01-27",
            "isOffDay": true
        }
    ]
}
```