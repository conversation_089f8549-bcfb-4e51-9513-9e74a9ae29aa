#pragma once

#include <SQLiteCpp/SQLiteCpp.h>
#include <memory>
#include <string>
#include <filesystem>
#include <spdlog/spdlog.h>

class DatabaseManager {
public:

    bool initialize(const std::string& db_path) {
        try {
            std::filesystem::path path(db_path);
            std::filesystem::create_directories(path.parent_path());

            db_ = std::make_unique<SQLite::Database>(db_path, SQLite::OPEN_READWRITE | SQLite::OPEN_CREATE);

            db_->exec("PRAGMA journal_mode=WAL");
            db_->exec("PRAGMA synchronous=NORMAL");
            db_->exec("PRAGMA cache_size=10000");
            db_->exec("PRAGMA temp_store=memory");

            return createTables();
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Database initialization failed: {}", e.what());
            return false;
        }
    }

    SQLite::Database& getDatabase() {
        if (!db_) {
            throw std::runtime_error("Database not initialized");
        }
        return *db_;
    }

    void close() {
        if (db_) {
            db_.reset();
        }
    }

    bool createTables() {
        try {
            return createProductsTable() &&
                   createProjectsTable() &&
                   createCustomersTable() &&
                   createUsersTable() &&
                   createWorkHoursTable() &&
                   createHolidaysTable() &&
                   createSyncStatusTable();
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create tables: {}", e.what());
            return false;
        }
    }

public:
    DatabaseManager() = default;
    std::unique_ptr<SQLite::Database> db_;

    bool createProductsTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                product_name TEXT NOT NULL,
                product_description TEXT,
                product_start_time TEXT,
                product_end_time TEXT,
                group_name TEXT
            )
        )";

        try {
            db_->exec(sql);
            // Remove sync_time column if it exists (for database migration)
            try {
                db_->exec("ALTER TABLE products DROP COLUMN sync_time");
            } catch (...) {
                // Column might not exist, ignore error
            }
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create products table: {}", e.what());
            return false;
        }
    }

    bool createProjectsTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY,
                project_code TEXT UNIQUE,
                project_name TEXT NOT NULL,
                short_name TEXT,
                start_time INTEGER,
                end_time INTEGER,
                plan_start_time INTEGER,
                plan_end_time INTEGER,
                duty_persons TEXT,
                duty_departments TEXT,
                join_departments TEXT,
                join_persons TEXT,
                project_type TEXT,
                project_state TEXT,
                rel_product_list TEXT,
                rel_customer_info INTEGER,
                project_comment TEXT,
                contract_name TEXT
            )
        )";

        try {
            db_->exec(sql);
            // Remove sync_time column if it exists (for database migration)
            try {
                db_->exec("ALTER TABLE projects DROP COLUMN sync_time");
            } catch (...) {
                // Column might not exist, ignore error
            }
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create projects table: {}", e.what());
            return false;
        }
    }

    bool createCustomersTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS customers (
                id TEXT PRIMARY KEY,
                unit_name TEXT NOT NULL,
                contact TEXT,
                department TEXT,
                phones TEXT,
                group_name TEXT
            )
        )";

        try {
            db_->exec(sql);
            // Remove sync_time column if it exists (for database migration)
            try {
                db_->exec("ALTER TABLE customers DROP COLUMN sync_time");
            } catch (...) {
                // Column might not exist, ignore error
            }
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create customers table: {}", e.what());
            return false;
        }
    }

    bool createUsersTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                account TEXT NOT NULL,
                name TEXT NOT NULL,
                gender TEXT,
                mobile TEXT,
                email TEXT,
                phone TEXT,
                post TEXT,
                type TEXT,
                enable INTEGER DEFAULT 1,
                dept_name TEXT,
                team_name TEXT,
                role_name TEXT
            )
        )";

        try {
            db_->exec(sql);
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create users table: {}", e.what());
            return false;
        }
    }

    bool createWorkHoursTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS work_hours (
                id INTEGER PRIMARY KEY,
                user_id TEXT NOT NULL,
                user_name TEXT NOT NULL,
                project_id INTEGER NOT NULL,
                product_id INTEGER,
                customer_id INTEGER,
                work_date TEXT NOT NULL,
                start_time INTEGER,
                end_time INTEGER,
                duration_hours REAL NOT NULL,
                work_description TEXT,
                work_type TEXT,
                created_time TEXT DEFAULT (datetime('now')),
                updated_time TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (project_id) REFERENCES projects(id),
                FOREIGN KEY (product_id) REFERENCES products(id),
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )
        )";

        try {
            db_->exec(sql);
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create work_hours table: {}", e.what());
            return false;
        }
    }



    bool createHolidaysTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS holidays (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year INTEGER NOT NULL,
                name TEXT NOT NULL,
                date TEXT NOT NULL UNIQUE,
                is_off_day INTEGER DEFAULT 0
            )
        )";

        try {
            db_->exec(sql);
            // Create index for better query performance
            db_->exec("CREATE INDEX IF NOT EXISTS idx_holidays_year ON holidays(year)");
            db_->exec("CREATE INDEX IF NOT EXISTS idx_holidays_date ON holidays(date)");
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create holidays table: {}", e.what());
            return false;
        }
    }

    bool createSyncStatusTable() {
        const std::string sql = R"(
            CREATE TABLE IF NOT EXISTS sync_status (
                data_type TEXT PRIMARY KEY,
                sync_time TEXT DEFAULT (datetime('now'))
            )
        )";

        try {
            db_->exec(sql);
            return true;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Failed to create sync_status table: {}", e.what());
            return false;
        }
    }
};
