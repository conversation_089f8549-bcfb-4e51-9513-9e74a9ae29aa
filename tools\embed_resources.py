#!/usr/bin/env python3
"""
Resource embedding tool for C++
Converts files to C++ header with embedded binary data
"""

import os
import sys
import argparse
from pathlib import Path

def file_to_cpp_array(file_path, var_name):
    """Convert file to C++ byte array"""
    with open(file_path, 'rb') as f:
        data = f.read()
    
    # Generate C++ array
    cpp_array = f"const unsigned char {var_name}[] = {{\n"
    
    # Add bytes in groups of 16
    for i in range(0, len(data), 16):
        chunk = data[i:i+16]
        hex_values = [f"0x{b:02x}" for b in chunk]
        cpp_array += "    " + ", ".join(hex_values)
        if i + 16 < len(data):
            cpp_array += ","
        cpp_array += "\n"
    
    cpp_array += "};\n"
    cpp_array += f"const size_t {var_name}_size = {len(data)};\n\n"
    
    return cpp_array

def get_mime_type(file_path):
    """Get MIME type based on file extension"""
    ext = Path(file_path).suffix.lower()
    mime_types = {
        '.html': 'text/html',
        '.css': 'text/css',
        '.js': 'application/javascript',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.txt': 'text/plain'
    }
    return mime_types.get(ext, 'application/octet-stream')

def generate_resource_header(web_dir, output_file):
    """Generate C++ header with embedded resources"""
    
    header_content = """#pragma once
#include <string>
#include <unordered_map>
#include <string_view>

namespace EmbeddedResources {

struct Resource {
    const unsigned char* data;
    size_t size;
    std::string mime_type;
};

"""
    
    # Collect all files
    resources = {}
    web_path = Path(web_dir)
    
    for file_path in web_path.rglob('*'):
        if file_path.is_file():
            # Get relative path from web directory
            rel_path = file_path.relative_to(web_path)
            url_path = '/' + str(rel_path).replace('\\', '/')
            
            # Generate variable name
            var_name = str(rel_path).replace('/', '_').replace('\\', '_').replace('.', '_').replace('-', '_')
            var_name = f"resource_{var_name}"
            
            # Add file data
            header_content += file_to_cpp_array(file_path, var_name)
            
            # Store resource info
            resources[url_path] = {
                'var_name': var_name,
                'mime_type': get_mime_type(file_path)
            }
    
    # Generate resource map
    header_content += "const std::unordered_map<std::string, Resource> resources = {\n"
    
    for url_path, info in resources.items():
        header_content += f'    {{"{url_path}", {{{info["var_name"]}, {info["var_name"]}_size, "{info["mime_type"]}"}}}},\n'
    
    header_content += "};\n\n"
    
    # Add helper functions
    header_content += """
inline const Resource* getResource(const std::string& path) {
    auto it = resources.find(path);
    return (it != resources.end()) ? &it->second : nullptr;
}

inline std::string getResourceContent(const std::string& path) {
    const Resource* res = getResource(path);
    if (res) {
        return std::string(reinterpret_cast<const char*>(res->data), res->size);
    }
    return "";
}

} // namespace EmbeddedResources
"""
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(header_content)
    
    print(f"Generated {output_file} with {len(resources)} resources")
    for url_path in sorted(resources.keys()):
        print(f"  {url_path}")

def main():
    parser = argparse.ArgumentParser(description='Embed web resources into C++ header')
    parser.add_argument('web_dir', help='Web directory to embed')
    parser.add_argument('output', help='Output header file')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.web_dir):
        print(f"Error: Web directory '{args.web_dir}' does not exist")
        sys.exit(1)
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    generate_resource_header(args.web_dir, args.output)

if __name__ == '__main__':
    main()
