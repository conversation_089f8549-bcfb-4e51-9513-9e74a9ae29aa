﻿# 需求文档

## 需求描述

采用 C++ 实现一个 B/S 架构的工时管理软件。
其组成如下:
- 后台服务`B`: 已经实现, 访问地址为 ***************:7000, 通信接口请查看 `api.md`
- 本软件服务端`S`: Web 服务，后台运行，定时调用 B 的接口同步数据；提供 RESTful API 和 Web 界面
- Web 客户端: 通过浏览器访问，提供用户交互界面，查看服务运行状态、同步状态、数据统计分析等

## 技术架构

### 核心技术栈
- **编程语言**: C++
- **数据库**: SQLite3 (文件名称为 dailyreport.db)
- **前端**: HTML5 + CSS3 + JavaScript
- **数据格式**: JSON
- **通信协议**: HTTP/HTTPS, WebSocket

## 功能要求

### 1. 数据同步模块

#### 1.1 同步数据类型
通过 HTTP 接口调用获取以下数据类型并存储在本地 SQLite3 数据库:
- **Product**: 产品信息
- **Project**: 项目信息
- **Customer**: 客户信息

#### 1.2 同步策略
- **自动同步**: 系统启动时自动同步，之后每12小时自动同步一次
- **手动同步**: 点击同步时间可触发手动同步
- **全量同步**: 每次同步获取完整数据并更新本地数据库
- **同步状态管理**: 使用独立的 sync_status 表统一管理所有数据类型的同步状态
- **同步记录**: 记录同步时间、状态、记录数量和错误信息

#### 1.3 数据库设计

##### 1.3.1 数据对象定义
- Product: 产品数据对象
- Project: 项目数据对象
- Customer: 客户数据对象
- SyncStatus: 同步状态数据对象

##### 1.3.2 数据库表结构 (SQL)
```sql
-- 产品表
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY,
    product_name TEXT NOT NULL,
    product_description TEXT,
    product_start_time TEXT,
    product_end_time TEXT,
    group_name TEXT
);

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY,
    project_code TEXT UNIQUE,
    project_name TEXT NOT NULL,
    short_name TEXT,
    start_time TEXT,
    end_time TEXT,
    plan_start_time TEXT,
    plan_end_time TEXT,
    duty_persons TEXT,
    duty_departments TEXT,
    join_departments TEXT,
    join_persons TEXT,
    project_type TEXT,
    project_state TEXT,
    rel_product_list TEXT, -- JSON 数组字符串
    rel_customer_info INTEGER,
    project_comment TEXT,
    contract_name TEXT
);

-- 客户表
CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY,
    unit_name TEXT NOT NULL,
    contact TEXT,
    department TEXT,
    phones TEXT,
    group_name TEXT
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    account TEXT NOT NULL,
    name TEXT NOT NULL,
    gender TEXT,
    mobile TEXT,
    email TEXT,
    phone TEXT,
    post TEXT,
    type TEXT,
    enable INTEGER DEFAULT 1,
    dept_name TEXT,
    team_name TEXT,
    role_name TEXT
);

-- 同步状态表
CREATE TABLE IF NOT EXISTS sync_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_type TEXT NOT NULL, -- 'product', 'project', 'customer'
    sync_time TEXT DEFAULT (datetime('now')),
    status TEXT NOT NULL, -- 'success', 'failed'
    record_count INTEGER DEFAULT 0,
    error_message TEXT
);
```

##### 1.3.3 数据库操作接口
数据库 CRUD 操作接口，支持：
- 创建、更新、查询、删除操作
- 参数化查询
- 事务管理

### 2. Web API 模块

#### 2.1 RESTful API 设计
提供以下 API 端点:

- **数据查询 API**: 获取产品、项目、客户列表
- **同步管理 API**: 手动同步各类数据，获取同步状态
- **工时管理 API**: 工时的增删改查操作
- **统计分析 API**: 各种维度的统计数据

#### 2.2 API 响应格式
```json
{
    "code": 0,           // 0: 成功, 非0: 错误码
    "message": "success", // 响应消息
    "data": {},          // 响应数据
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### 3. 工时录入模块

#### 3.1 工时数据结构

##### 3.1.1 工时数据对象定义
WorkHour: 工时数据对象，包含用户信息、项目关联、时间记录等字段

##### 3.1.2 工时表结构
```sql
CREATE TABLE IF NOT EXISTS work_hours (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    user_name TEXT NOT NULL,
    project_id INTEGER NOT NULL,
    product_id INTEGER,
    customer_id INTEGER,
    work_date TEXT NOT NULL,
    start_time TEXT,
    end_time TEXT,
    duration_hours REAL NOT NULL, -- 工时数（小时）
    work_description TEXT,
    work_type TEXT, -- '开发', '测试', '设计', '会议' 等
    created_time TEXT DEFAULT (datetime('now')),
    updated_time TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

##### 3.1.3 工时数据库操作
工时数据 CRUD 操作，支持：
- 创建、更新、删除工时记录
- 按 ID、日期范围、项目查询
- 参数化查询

#### 3.2 录入功能
- 支持按项目、产品、客户维度录入工时
- 支持工时类型分类（开发、测试、设计、会议等）
- 支持批量录入和单条录入
- 数据验证：工时合理性检查、重复录入检查

### 4. 工时统计模块

#### 4.1 统计维度
- **时间维度**: 日、周、月、季度、年
- **项目维度**: 按项目统计工时分布
- **人员维度**: 按人员统计工时情况
- **类型维度**: 按工作类型统计工时分布

#### 4.2 统计图表
- 工时趋势图（折线图）
- 项目工时分布图（饼图）
- 人员工时对比图（柱状图）
- 工时类型分布图（环形图）

### 5. Web 界面模块

#### 5.1 页面结构（当前实现）
- **工时管理主页**: 标签页式导航，包含统计和基础数据管理
  - **Reports 标签**: 工时统计分析
    - 时间段选择：本周、本月、自定义时间段
    - 统计概览：总工时、总记录数、活跃用户数、活跃项目数
    - 用户统计：按用户分组的工时统计
    - 项目统计：按项目分组的工时统计
  - **Projects 标签**: 项目列表，显示项目代码、名称、关联产品、客户、备注
  - **Products 标签**: 产品列表，显示产品 ID、名称、描述
  - **Customers 标签**: 客户列表，显示客户 ID、单位名称、分组
- **数据交互功能**:
  - 表格排序：点击表头可对任意列进行升序/降序排序
  - 数据筛选：项目页面支持按产品和客户筛选
  - 详情查看：点击产品名称或客户名称弹出详情面板
  - 关联跳转：项目表格中的产品和客户支持点击查看详情
- **同步状态显示**: 标题显示数据数量和最后同步时间

#### 5.2 界面设计特点
- **现代化 UI**: 使用渐变背景、阴影效果、微动画等现代设计元素
- **紧凑布局**: 优化字体大小和间距，最大化内容显示密度
- **响应式设计**: 支持不同屏幕尺寸，最大宽度 1400px
- **交互反馈**: 悬停效果、过渡动画、视觉反馈
- **无头设计**: 去除传统页面头部，直接显示功能内容

## 部署要求

### 5.1 系统要求
- 操作系统: Windows 10/11, Linux (Ubuntu 20.04+), macOS
- 内存: 最小 512MB，推荐 1GB+
- 磁盘: 最小 100MB，推荐 1GB+（用于数据存储）
- 网络: 能够访问后台服务 ***************:7000

### 5.2 配置文件
```json
{
    "server": {
        "host": "0.0.0.0",
        "port": 8080,
        "threads": 4
    },
    "database": {
        "path": "./data/dailyreport.db",
        "connection_pool_size": 10,
        "enable_wal_mode": true,
        "busy_timeout": 30000
    },
    "backend": {
        "base_url": "http://***************:7000/atom/v1/workhour/api",
        "timeout": 30,
        "retry_count": 3
    },
    "sync": {
        "interval_minutes": 60,
        "auto_start": true
    }
}
```

### 5.3 启动方式
- 支持命令行启动
- 支持 Windows 服务/Linux systemd 服务
- 支持 Docker 容器化部署

## 当前实现状态

### ✅ 已实现功能

#### 数据同步模块
- ✅ 产品、项目、客户、用户数据同步
- ✅ 自动同步（启动时 + 12小时间隔）
- ✅ 工时数据增量同步（30分钟间隔）
- ✅ 手动同步（点击同步时间触发）
- ✅ 同步状态管理（独立 sync_status 表）
- ✅ 错误处理和重试机制

#### Web API 模块
- ✅ RESTful API 设计
- ✅ 数据查询 API（产品、项目、客户、用户）
- ✅ 工时管理 API（创建、查询、更新、删除工时记录）
- ✅ 工时统计 API（多维度统计分析）
- ✅ 同步管理 API（手动同步、状态查询、增量同步）
- ✅ 统一 JSON 响应格式

#### 数据库模块
- ✅ SQLite3 数据库
- ✅ 完整的表结构设计
- ✅ DAO 模式数据访问层
- ✅ 数据库连接管理

#### Web 界面模块
- ✅ 现代化单页应用
- ✅ 标签页导航（My Reports/Statistics/Projects/Products/Customers）
- ✅ 工时录入表单（项目选择、时间计算、工作描述）
- ✅ 工时统计界面（时间段选择、多维度统计展示）
- ✅ 表格排序和筛选功能
- ✅ 详情查看模态框
- ✅ 关联数据跳转
- ✅ 实时同步状态显示
- ✅ 响应式设计

### 🚧 待实现功能

#### 工时录入模块
- ✅ 工时数据表结构
- ✅ 工时录入界面
- ✅ 工时数据 CRUD API
- ⏳ 数据验证和重复检查
- ⏳ 工时记录编辑功能

#### 工时统计模块
- ✅ 多维度统计分析
- ✅ 统计数据展示
- ⏳ 统计图表展示（图形化）
- ⏳ 报表导出功能

#### 系统管理
- ⏳ 用户管理
- ⏳ 权限控制
- ⏳ 系统配置界面