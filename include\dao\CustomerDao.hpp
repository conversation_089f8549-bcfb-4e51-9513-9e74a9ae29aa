#pragma once

#include "database/DatabaseManager.hpp"
#include "models/Customer.hpp"
#include <optional>
#include <spdlog/spdlog.h>
#include <vector>

class CustomerDao
{
public:
    CustomerDao(DatabaseManager &db_manager)
        : db_manager_(db_manager)
    {
    }

    std::vector<Customer> findAll()
    {
        std::vector<Customer> customers;
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM customers ORDER BY id");

            while (query.executeStep())
            {
                Customer customer;
                customer.id = query.getColumn(0).getString();
                customer.unit_name = query.getColumn(1).getString();
                customer.contact = query.getColumn(2).getString();
                customer.department = query.getColumn(3).getString();
                customer.phones = query.getColumn(4).getString();
                customer.group_name = query.getColumn(5).getString();
                customers.push_back(customer);
            }
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("CustomerDao::findAll error: {}", e.what());
        }
        return customers;
    }

    bool batchInsertOrUpdate(const std::vector<Customer> &customers)
    {
        if (customers.empty())
        {
            return true;
        }

        int processed = 0;
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO customers (id, unit_name, contact, department, phones, group_name)
                VALUES (?, ?, ?, ?, ?, ?)
            )");

            for (const auto &customer : customers)
            {
                query.bind(1, customer.id);
                query.bind(2, customer.unit_name);
                query.bind(3, customer.contact);
                query.bind(4, customer.department);
                query.bind(5, customer.phones);
                query.bind(6, customer.group_name);
                processed++;
            }

            transaction.commit();
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("CustomerDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
        return processed == customers.size();
    }

private:
    DatabaseManager &db_manager_;
};
