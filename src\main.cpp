#include "core/Application.hpp"
#include <spdlog/spdlog.h>

int main(int argc, char *argv[])
{
#ifdef _WIN32
    // Set console to UTF-8 encoding for proper character display
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    spdlog::set_level(spdlog::level::debug);
    spdlog::set_pattern("[%Y%m%d %H:%M:%S.%e][%L] %v. [%s:%#, %t]");

    try
    {
        Application app("config.json");
        app.start();
    }
    catch (const std::exception &e)
    {
        SPDLOG_ERROR("Application error: {}", e.what());
        return 1;
    }

    return 0;
}
