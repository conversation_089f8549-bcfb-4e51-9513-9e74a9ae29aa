#pragma once

#include "models/WorkHour.hpp"
#include "database/DatabaseManager.hpp"
#include <vector>
#include <optional>
#include <map>
#include <chrono>
#include <ctime>
#include <spdlog/spdlog.h>

class WorkHourDao {
public:
    WorkHourDao(DatabaseManager& db_manager) : db_manager_(db_manager) {}
    
    bool insert(const WorkHour& workHour) {
        try {
            auto& db = db_manager_.getDatabase();

            // Use insertWithId if workHour has an ID, otherwise generate one
            if (workHour.id > 0) {
                return insertWithId(workHour);
            }

            // For records without ID, we need to generate one or handle differently
            // This should rarely happen with API data, but handle for completeness
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO work_hours (user_id, user_name, project_id, product_id, customer_id,
                                      work_date, start_time, end_time, duration_hours, work_description,
                                      work_type, created_time, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            )");

            query.bind(1, workHour.user_id);
            query.bind(2, workHour.user_name);
            query.bind(3, workHour.project_id);
            if (workHour.product_id == 0) {
                query.bind(4);  // bind NULL
            } else {
                query.bind(4, workHour.product_id);
            }
            if (workHour.customer_id == 0) {
                query.bind(5);  // bind NULL
            } else {
                query.bind(5, workHour.customer_id);
            }
            query.bind(6, workHour.work_date);
            query.bind(7, workHour.start_time);
            query.bind(8, workHour.end_time);
            query.bind(9, workHour.duration_hours);
            query.bind(10, workHour.work_description);
            query.bind(11, workHour.work_type);

            // Use provided times or current time as fallback
            if (!workHour.created_time.empty()) {
                query.bind(12, workHour.created_time);
            }
            if (!workHour.updated_time.empty()) {
                query.bind(13, workHour.updated_time);
            }
            
            return query.exec() > 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::insert error: {}", e.what());
            return false;
        }
    }
    
    bool update(const WorkHour& workHour) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                UPDATE work_hours SET user_id=?, user_name=?, project_id=?, product_id=?, customer_id=?,
                                    work_date=?, start_time=?, end_time=?, duration_hours=?, work_description=?,
                                    work_type=?, updated_time=?
                WHERE id=?
            )");

            query.bind(1, workHour.user_id);
            query.bind(2, workHour.user_name);
            query.bind(3, workHour.project_id);
            if (workHour.product_id == 0) {
                query.bind(4);  // bind NULL
            } else {
                query.bind(4, workHour.product_id);
            }
            if (workHour.customer_id == 0) {
                query.bind(5);  // bind NULL
            } else {
                query.bind(5, workHour.customer_id);
            }
            query.bind(6, workHour.work_date);
            query.bind(7, workHour.start_time);
            query.bind(8, workHour.end_time);
            query.bind(9, workHour.duration_hours);
            query.bind(10, workHour.work_description);
            query.bind(11, workHour.work_type);

            // Use provided updated_time or current time as fallback
            if (!workHour.updated_time.empty()) {
                query.bind(12, workHour.updated_time);
            } else {
                // Generate current time string
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                std::tm* tm = std::gmtime(&time_t);
                char time_str[20];
                std::strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm);
                query.bind(12, std::string(time_str));
            }
            query.bind(13, workHour.id);
            
            return query.exec() > 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::update error: {}", e.what());
            return false;
        }
    }
    
    bool deleteById(int id) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "DELETE FROM work_hours WHERE id=?");
            query.bind(1, id);
            return query.exec() > 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::deleteById error: {}", e.what());
            return false;
        }
    }
    
    std::optional<WorkHour> findById(int id) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM work_hours WHERE id=?");
            query.bind(1, id);
            
            if (query.executeStep()) {
                return parseWorkHour(query);
            }
            return std::nullopt;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::findById error: {}", e.what());
            return std::nullopt;
        }
    }
    
    std::vector<WorkHour> findByUserId(const std::string& userId) {
        std::vector<WorkHour> workHours;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM work_hours WHERE user_id=? ORDER BY work_date DESC, created_time DESC");
            query.bind(1, userId);
            
            while (query.executeStep()) {
                workHours.push_back(parseWorkHour(query));
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::findByUserId error: {}", e.what());
        }
        return workHours;
    }
    
    std::vector<WorkHour> findByDateRange(const std::string& startDate, const std::string& endDate) {
        std::vector<WorkHour> workHours;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM work_hours WHERE work_date BETWEEN ? AND ? ORDER BY work_date DESC, created_time DESC");
            query.bind(1, startDate);
            query.bind(2, endDate);
            
            while (query.executeStep()) {
                workHours.push_back(parseWorkHour(query));
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::findByDateRange error: {}", e.what());
        }
        return workHours;
    }
    
    WorkHourStatistics getStatistics(const std::string& startDate, const std::string& endDate) {
        WorkHourStatistics stats;
        try {
            auto& db = db_manager_.getDatabase();
            
            // Get overall statistics
            SQLite::Statement overallQuery(db, R"(
                SELECT 
                    COALESCE(SUM(duration_hours), 0) as total_hours,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT user_id) as active_users,
                    COUNT(DISTINCT project_id) as active_projects
                FROM work_hours 
                WHERE work_date BETWEEN ? AND ?
            )");
            overallQuery.bind(1, startDate);
            overallQuery.bind(2, endDate);
            
            if (overallQuery.executeStep()) {
                stats.total_hours = overallQuery.getColumn(0).getDouble();
                stats.total_records = overallQuery.getColumn(1).getInt();
                stats.active_users = overallQuery.getColumn(2).getInt();
                stats.active_projects = overallQuery.getColumn(3).getInt();
            }
            
            // Get statistics by user
            SQLite::Statement userQuery(db, R"(
                SELECT 
                    user_id,
                    user_name,
                    COALESCE(SUM(duration_hours), 0) as total_hours,
                    COUNT(*) as record_count,
                    COUNT(DISTINCT work_date) as work_days
                FROM work_hours 
                WHERE work_date BETWEEN ? AND ?
                GROUP BY user_id, user_name
                ORDER BY total_hours DESC
            )");
            userQuery.bind(1, startDate);
            userQuery.bind(2, endDate);
            
            while (userQuery.executeStep()) {
                WorkHourStatistics::UserStats userStats;
                userStats.user_id = userQuery.getColumn(0).getString();
                userStats.user_name = userQuery.getColumn(1).getString();
                userStats.total_hours = userQuery.getColumn(2).getDouble();
                userStats.record_count = userQuery.getColumn(3).getInt();
                userStats.work_days = userQuery.getColumn(4).getInt();
                stats.by_user.push_back(userStats);
            }
            
            // Get statistics by project
            SQLite::Statement projectQuery(db, R"(
                SELECT 
                    project_id,
                    COALESCE(SUM(duration_hours), 0) as total_hours,
                    COUNT(*) as record_count,
                    COUNT(DISTINCT user_id) as user_count
                FROM work_hours 
                WHERE work_date BETWEEN ? AND ?
                GROUP BY project_id
                ORDER BY total_hours DESC
            )");
            projectQuery.bind(1, startDate);
            projectQuery.bind(2, endDate);
            
            while (projectQuery.executeStep()) {
                WorkHourStatistics::ProjectStats projectStats;
                projectStats.project_id = projectQuery.getColumn(0).getInt();
                projectStats.total_hours = projectQuery.getColumn(1).getDouble();
                projectStats.record_count = projectQuery.getColumn(2).getInt();
                projectStats.user_count = projectQuery.getColumn(3).getInt();
                stats.by_project.push_back(projectStats);
            }
            
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::getStatistics error: {}", e.what());
        }
        return stats;
    }
    
    int getCount() {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT COUNT(*) FROM work_hours");
            if (query.executeStep()) {
                return query.getColumn(0).getInt();
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::getCount error: {}", e.what());
        }
        return 0;
    }

    bool insertOrUpdate(const WorkHour& workHour) {
        try {
            auto& db = db_manager_.getDatabase();

            // If workHour has an ID from the API, use it to check for existing records
            if (workHour.id > 0) {
                SQLite::Statement checkQuery(db, R"(
                    SELECT id FROM work_hours WHERE id=? LIMIT 1
                )");
                checkQuery.bind(1, workHour.id);

                if (checkQuery.executeStep()) {
                    // Record with this ID exists, update it
                    SPDLOG_DEBUG("WorkHourDao: Updating existing work hour with ID: {}", workHour.id);
                    return update(workHour);
                } else {
                    // Record with this ID doesn't exist, insert it with the specific ID
                    SPDLOG_DEBUG("WorkHourDao: Inserting new work hour with API ID: {}", workHour.id);
                    return insertWithId(workHour);
                }
            } else {
                // No API ID, check for duplicates based on content to avoid duplicates
                SQLite::Statement checkQuery(db, R"(
                    SELECT id FROM work_hours
                    WHERE user_id=? AND work_date=? AND project_id=? AND work_description=? AND duration_hours=?
                    LIMIT 1
                )");
                checkQuery.bind(1, workHour.user_id);
                checkQuery.bind(2, workHour.work_date);
                checkQuery.bind(3, workHour.project_id);
                checkQuery.bind(4, workHour.work_description);
                checkQuery.bind(5, workHour.duration_hours);

                if (checkQuery.executeStep()) {
                    // Similar record exists, update it
                    WorkHour existingWorkHour = workHour;
                    existingWorkHour.id = checkQuery.getColumn(0).getInt();
                    SPDLOG_DEBUG("WorkHourDao: Updating similar work hour with local ID: {}", existingWorkHour.id);
                    return update(existingWorkHour);
                } else {
                    // No similar record, insert new one
                    SPDLOG_DEBUG("WorkHourDao: Inserting new work hour without API ID");
                    return insert(workHour);
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::insertOrUpdate error: {}", e.what());
            return false;
        }
    }

    bool insertWithId(const WorkHour& workHour) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO work_hours (id, user_id, user_name, project_id, product_id, customer_id,
                                      work_date, start_time, end_time, duration_hours, work_description,
                                      work_type, created_time, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            )");

            query.bind(1, workHour.id);
            query.bind(2, workHour.user_id);
            query.bind(3, workHour.user_name);
            query.bind(4, workHour.project_id);
            query.bind(5, workHour.product_id);
            query.bind(6, workHour.customer_id);
            query.bind(7, workHour.work_date);
            query.bind(8, workHour.start_time);
            query.bind(9, workHour.end_time);
            query.bind(10, workHour.duration_hours);
            query.bind(11, workHour.work_description);
            query.bind(12, workHour.work_type);
            query.bind(13, workHour.created_time);
            query.bind(14, workHour.updated_time);

            int result = query.exec();
            SPDLOG_DEBUG("WorkHourDao: Inserted work hour with ID: {}, affected rows: {}", workHour.id, result);
            return result > 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::insertWithId error: {}", e.what());
            return false;
        }
    }

    // Batch insert/update for better performance during sync
    bool batchInsertOrUpdate(const std::vector<WorkHour>& workHours) {
        if (workHours.empty()) {
            return true;
        }

        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            int processed = 0;
            for (const auto& workHour : workHours) {
                if (insertOrUpdate(workHour)) {
                    processed++;
                }
            }

            transaction.commit();
            SPDLOG_INFO("WorkHourDao: Batch operation completed - {}/{} records processed successfully",
                        processed, workHours.size());
            return processed == workHours.size();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("WorkHourDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
    }

private:
    DatabaseManager& db_manager_;
    
    WorkHour parseWorkHour(SQLite::Statement& query) {
        WorkHour workHour;
        workHour.id = query.getColumn(0).getInt();
        workHour.user_id = query.getColumn(1).getString();
        workHour.user_name = query.getColumn(2).getString();
        workHour.project_id = query.getColumn(3).getInt();
        workHour.product_id = query.getColumn(4).isNull() ? 0 : query.getColumn(4).getInt();
        workHour.customer_id = query.getColumn(5).isNull() ? 0 : query.getColumn(5).getInt();
        workHour.work_date = query.getColumn(6).getString();
        workHour.start_time = query.getColumn(7).getString();
        workHour.end_time = query.getColumn(8).getString();
        workHour.duration_hours = query.getColumn(9).getDouble();
        workHour.work_description = query.getColumn(10).getString();
        workHour.work_type = query.getColumn(11).getString();
        workHour.created_time = query.getColumn(12).getString();
        workHour.updated_time = query.getColumn(13).getString();
        return workHour;
    }
};
