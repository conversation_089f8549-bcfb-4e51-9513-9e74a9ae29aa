<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Query Tool</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #fafafa;
            height: 100vh;
            display: flex;
            flex-direction: column;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .header {
            background: #fff;
            color: #000;
            padding: 24px 32px;
            border-bottom: 1px solid #eaeaea;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            letter-spacing: -0.025em;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 12px;
            transition: all 0.2s ease;
            border: 1px solid #eaeaea;
        }

        .back-link:hover {
            color: #000;
            background-color: #fafafa;
            border-color: #000;
        }

        .back-icon {
            width: 16px;
            height: 16px;
        }

        .query-section {
            background: #fff;
            padding: 24px 32px;
            margin: 0;
            border-bottom: 1px solid #eaeaea;
        }

        .query-input {
            display: flex;
            gap: 16px;
            align-items: flex-start;
        }

        #sqlInput {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #eaeaea;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
            background-color: #fafafa;
            line-height: 1.5;
        }

        #sqlInput:focus {
            outline: none;
            border-color: #000;
            box-shadow: 0 0 0 2px rgba(0,0,0,0.1);
            background-color: #fff;
        }

        .btn {
            padding: 12px 24px;
            background: #000;
            color: #fff;
            border: 1px solid #000;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            font-family: inherit;
            font-size: 14px;
        }

        .btn:hover {
            background: #333;
            border-color: #333;
        }

        .content {
            flex: 1;
            display: flex;
            margin: 0;
            gap: 0;
            min-height: 0;
        }

        .sidebar {
            width: 280px;
            background: #fafafa;
            border-right: 1px solid #eaeaea;
            overflow: hidden;
        }

        .sidebar-header {
            background: #fff;
            padding: 20px 24px;
            border-bottom: 1px solid #eaeaea;
            font-weight: 600;
            color: #000;
            font-size: 14px;
        }

        .table-list {
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .table-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 1px solid #eaeaea;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #666;
        }

        .table-item:hover {
            background-color: #f5f5f5;
            color: #000;
        }

        .table-item.active {
            background-color: #000;
            color: #fff;
            font-weight: 500;
        }

        .main-content {
            flex: 1;
            background: #fff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 20px 32px;
            border-bottom: 1px solid #eaeaea;
            font-weight: 600;
            color: #000;
            font-size: 16px;
        }

        .table-container {
            flex: 1;
            overflow: auto;
            padding: 32px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            border: 1px solid #eaeaea;
            overflow: hidden;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #eaeaea;
        }

        .data-table th {
            background-color: #fafafa;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1;
            color: #000;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .data-table th:hover {
            background-color: #f0f0f0;
        }

        .sort-indicator {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #666;
        }

        .sort-indicator.asc::after {
            content: '▲';
        }

        .sort-indicator.desc::after {
            content: '▼';
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 32px;
            background: #fff;
            border-top: 1px solid #eaeaea;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 12px;
            background: #fff;
            color: #000;
            border: 1px solid #eaeaea;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f5f5f5;
            border-color: #000;
        }

        .pagination-btn:disabled {
            background: #fafafa;
            color: #ccc;
            cursor: not-allowed;
            border-color: #eaeaea;
        }

        .page-input {
            width: 60px;
            padding: 6px 8px;
            border: 1px solid #eaeaea;
            text-align: center;
            font-size: 14px;
        }

        .page-input:focus {
            outline: none;
            border-color: #000;
        }

        .data-table tr:nth-child(even) {
            background-color: #fafafa;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .loading {
            text-align: center;
            padding: 60px 32px;
            color: #666;
            font-size: 14px;
        }

        .error {
            background-color: #ffeaea;
            color: #e00;
            padding: 16px 20px;
            margin: 20px;
            border: 1px solid #f5c6cb;
            font-size: 14px;
        }

        .empty-state {
            text-align: center;
            padding: 80px 32px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 12px;
            color: #000;
            font-size: 18px;
            font-weight: 600;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <a href="/" class="back-link">
                <svg class="back-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Dashboard
            </a>
            Database Query Tool
        </h1>
    </div>

    <div class="query-section">
        <div class="query-input">
            <textarea id="sqlInput" placeholder="Enter your SQL query here (SELECT statements only)..."></textarea>
            <button class="btn" onclick="executeQuery()">Execute Query</button>
        </div>
    </div>

    <div class="content">
        <div class="sidebar">
            <div class="sidebar-header">Tables</div>
            <div class="table-list" id="tableList">
                <div class="loading">Loading tables...</div>
            </div>
        </div>

        <div class="main-content">
            <div class="content-header" id="contentHeader">Select a table or execute a query</div>
            <div class="table-container" id="tableContainer">
                <div class="empty-state">
                    <h3>Welcome to Database Query Tool</h3>
                    <p>Select a table from the left sidebar to view its data, or enter a SQL query above.</p>
                </div>
            </div>
            <div class="pagination" id="pagination" style="display: none;">
                <div class="pagination-info" id="paginationInfo"></div>
                <div class="pagination-controls">
                    <button class="pagination-btn" id="firstPageBtn" onclick="goToPage(1)">First</button>
                    <button class="pagination-btn" id="prevPageBtn" onclick="goToPage(currentPage - 1)">Previous</button>
                    <span>Page</span>
                    <input type="number" class="page-input" id="pageInput" min="1" onchange="goToPageInput()" onkeypress="handlePageInputKeypress(event)">
                    <span>of <span id="totalPages">1</span></span>
                    <button class="pagination-btn" id="nextPageBtn" onclick="goToPage(currentPage + 1)">Next</button>
                    <button class="pagination-btn" id="lastPageBtn" onclick="goToPage(totalPages)">Last</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTable = null;
        let allTableData = null;
        let currentPage = 1;
        let pageSize = 100;
        let totalPages = 1;
        let sortConfig = { column: null, direction: 'asc' };

        // Load tables on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadTables();
        });

        async function loadTables() {
            try {
                const response = await fetch('/api/db/tables');
                const result = await response.json();
                
                if (result.code === 0) {
                    displayTables(result.data);
                } else {
                    showError('Failed to load tables: ' + result.message);
                }
            } catch (error) {
                showError('Error loading tables: ' + error.message);
            }
        }

        function displayTables(tables) {
            const tableList = document.getElementById('tableList');
            tableList.innerHTML = '';
            
            tables.forEach(table => {
                const tableItem = document.createElement('div');
                tableItem.className = 'table-item';
                tableItem.textContent = table;
                tableItem.onclick = () => selectTable(table);
                tableList.appendChild(tableItem);
            });
        }

        async function selectTable(tableName) {
            // Update active state
            document.querySelectorAll('.table-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            currentTable = tableName;
            document.getElementById('contentHeader').textContent = `Table: ${tableName}`;
            document.getElementById('tableContainer').innerHTML = '<div class="loading">Loading data...</div>';
            
            try {
                const response = await fetch(`/api/db/table/${tableName}`);
                const result = await response.json();
                
                if (result.code === 0) {
                    allTableData = result.data;
                    currentPage = 1;
                    sortConfig = { column: null, direction: 'asc' };
                    displayTableData();
                } else {
                    showError('Failed to load table data: ' + result.message);
                }
            } catch (error) {
                showError('Error loading table data: ' + error.message);
            }
        }

        async function executeQuery() {
            const sql = document.getElementById('sqlInput').value.trim();
            if (!sql) {
                alert('Please enter a SQL query');
                return;
            }
            
            document.getElementById('contentHeader').textContent = 'Query Results';
            document.getElementById('tableContainer').innerHTML = '<div class="loading">Executing query...</div>';
            
            // Clear table selection
            document.querySelectorAll('.table-item').forEach(item => {
                item.classList.remove('active');
            });
            currentTable = null;
            
            try {
                const response = await fetch('/api/db/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sql: sql })
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    allTableData = result.data;
                    currentPage = 1;
                    sortConfig = { column: null, direction: 'asc' };
                    displayTableData();
                } else {
                    showError('Query failed: ' + result.message);
                }
            } catch (error) {
                showError('Error executing query: ' + error.message);
            }
        }

        function displayTableData() {
            const container = document.getElementById('tableContainer');
            const paginationDiv = document.getElementById('pagination');

            if (!allTableData || !allTableData.rows || allTableData.rows.length === 0) {
                container.innerHTML = '<div class="empty-state"><h3>No Data</h3><p>The table is empty or query returned no results.</p></div>';
                paginationDiv.style.display = 'none';
                return;
            }

            // Apply sorting if configured
            let sortedData = [...allTableData.rows];
            if (sortConfig.column !== null) {
                const columnIndex = sortConfig.column;
                sortedData.sort((a, b) => {
                    let aVal = a[columnIndex];
                    let bVal = b[columnIndex];

                    // Handle null values
                    if (aVal === null && bVal === null) return 0;
                    if (aVal === null) return sortConfig.direction === 'asc' ? -1 : 1;
                    if (bVal === null) return sortConfig.direction === 'asc' ? 1 : -1;

                    // Convert to strings for comparison
                    aVal = String(aVal).toLowerCase();
                    bVal = String(bVal).toLowerCase();

                    // Try to parse as numbers if possible
                    const aNum = parseFloat(aVal);
                    const bNum = parseFloat(bVal);
                    if (!isNaN(aNum) && !isNaN(bNum)) {
                        aVal = aNum;
                        bVal = bNum;
                    }

                    if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
                    if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
                    return 0;
                });
            }

            // Calculate pagination
            const totalRows = sortedData.length;
            totalPages = Math.ceil(totalRows / pageSize);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, totalRows);
            const pageData = sortedData.slice(startIndex, endIndex);

            // Build table HTML
            let html = '<table class="data-table"><thead><tr>';

            // Add column headers with sort indicators
            allTableData.columns.forEach((column, index) => {
                const sortIndicator = sortConfig.column === index ?
                    `<span class="sort-indicator ${sortConfig.direction}"></span>` :
                    '<span class="sort-indicator"></span>';
                html += `<th onclick="sortTable(${index})">${escapeHtml(column)}${sortIndicator}</th>`;
            });
            html += '</tr></thead><tbody>';

            // Add data rows for current page
            pageData.forEach(row => {
                html += '<tr>';
                row.forEach(cell => {
                    const cellValue = cell === null ? '<em>NULL</em>' : escapeHtml(String(cell));
                    html += `<td>${cellValue}</td>`;
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            container.innerHTML = html;

            // Update pagination
            updatePagination(totalRows, startIndex + 1, endIndex);
        }

        function sortTable(columnIndex) {
            if (sortConfig.column === columnIndex) {
                // Toggle direction if same column
                sortConfig.direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // New column, start with ascending
                sortConfig.column = columnIndex;
                sortConfig.direction = 'asc';
            }

            // Reset to first page when sorting
            currentPage = 1;
            displayTableData();
        }

        function updatePagination(totalRows, startRow, endRow) {
            const paginationDiv = document.getElementById('pagination');
            const paginationInfo = document.getElementById('paginationInfo');
            const pageInput = document.getElementById('pageInput');
            const totalPagesSpan = document.getElementById('totalPages');
            const firstPageBtn = document.getElementById('firstPageBtn');
            const prevPageBtn = document.getElementById('prevPageBtn');
            const nextPageBtn = document.getElementById('nextPageBtn');
            const lastPageBtn = document.getElementById('lastPageBtn');

            if (totalRows === 0) {
                paginationDiv.style.display = 'none';
                return;
            }

            paginationDiv.style.display = 'flex';
            paginationInfo.textContent = `Showing ${startRow}-${endRow} of ${totalRows} records`;
            pageInput.value = currentPage;
            totalPagesSpan.textContent = totalPages;

            // Update button states
            firstPageBtn.disabled = currentPage === 1;
            prevPageBtn.disabled = currentPage === 1;
            nextPageBtn.disabled = currentPage === totalPages;
            lastPageBtn.disabled = currentPage === totalPages;
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            currentPage = page;
            displayTableData();
        }

        function goToPageInput() {
            const pageInput = document.getElementById('pageInput');
            const page = parseInt(pageInput.value);
            if (!isNaN(page)) {
                goToPage(page);
            }
        }

        function handlePageInputKeypress(event) {
            if (event.key === 'Enter') {
                goToPageInput();
            }
        }

        function showError(message) {
            const container = document.getElementById('tableContainer');
            const paginationDiv = document.getElementById('pagination');
            container.innerHTML = `<div class="error">${escapeHtml(message)}</div>`;
            paginationDiv.style.display = 'none';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Handle Enter key in textarea (Ctrl+Enter to execute)
        document.getElementById('sqlInput').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                executeQuery();
            }
        });
    </script>
</body>
</html>
