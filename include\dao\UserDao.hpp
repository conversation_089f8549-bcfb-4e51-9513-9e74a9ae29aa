﻿#pragma once

#include "models/User.hpp"
#include "database/DatabaseManager.hpp"
#include <vector>
#include <optional>
#include <spdlog/spdlog.h>

class UserDao {
public:
    UserDao(DatabaseManager& db_manager) : db_manager_(db_manager) {}
    
    bool insert(const User& user) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO users (id, account, name, gender, mobile, email, phone, post, type, enable, dept_name, team_name, role_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            )");

            query.bind(1, user.id);
            query.bind(2, user.account);
            query.bind(3, user.name);
            query.bind(4, user.gender);
            query.bind(5, user.mobile);
            query.bind(6, user.email);
            query.bind(7, user.phone);
            query.bind(8, user.post);
            query.bind(9, user.type);
            query.bind(10, user.enable ? 1 : 0);
            query.bind(11, user.deptName);
            query.bind(12, user.teamName);
            query.bind(13, user.roleName);

            return query.exec() > 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("UserDao::insert error: {}", e.what());
            return false;
        }
    }
    
    std::vector<User> findAll() {
        std::vector<User> users;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM users ORDER BY id");

            while (query.executeStep()) {
                User user;
                user.id = query.getColumn(0).getString();
                user.account = query.getColumn(1).getString();
                user.name = query.getColumn(2).getString();
                user.gender = query.getColumn(3).getString();
                user.mobile = query.getColumn(4).getString();
                user.email = query.getColumn(5).getString();
                user.phone = query.getColumn(6).getString();
                user.post = query.getColumn(7).getString();
                user.type = query.getColumn(8).getString();
                user.enable = query.getColumn(9).getInt() == 1;
                user.deptName = query.getColumn(10).getString();
                user.teamName = query.getColumn(11).getString();
                user.roleName = query.getColumn(12).getString();
                users.push_back(user);
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("UserDao::findAll error: {}", e.what());
        }
        return users;
    }
    
    bool insertOrUpdate(const User& user) {
        return insert(user);
    }

    bool batchInsertOrUpdate(const std::vector<User>& users) {
        if (users.empty()) {
            return true;
        }

        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            int processed = 0;
            for (const auto& user : users) {
                if (insertOrUpdate(user)) {
                    processed++;
                }
            }

            transaction.commit();
            SPDLOG_INFO("UserDao: Batch operation completed - {}/{} records processed successfully",
                        processed, users.size());
            return processed == users.size();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("UserDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
    }

private:
    DatabaseManager& db_manager_;
};
