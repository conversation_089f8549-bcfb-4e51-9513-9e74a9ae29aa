﻿{
    "code": 0,
    "msg": "ok",
    "data": [
        {
            "id": 8,
            "productName": "FTS",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 9,
            "productName": "FAES",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 10,
            "productName": "DRA",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 11,
            "productName": "å¢¨é¸¢",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 12,
            "productName": "çŽ„æ­¦",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 13,
            "productName": "SMCS",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 14,
            "productName": "AtomSim",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 15,
            "productName": "CSM",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 16,
            "productName": "CTS-MCS",
            "productDescription": "CTSä¾¿æºç«™ç›‘æŽ§è½¯ä»¶",
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 19,
            "productName": "ATS",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 20,
            "productName": "SMCS-RC",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        },
        {
            "id": 21,
            "productName": "SMCS-Manager",
            "productDescription": null,
            "productStartTime": null,
            "productEndTime": null,
            "groupName": null
        }
    ]
}