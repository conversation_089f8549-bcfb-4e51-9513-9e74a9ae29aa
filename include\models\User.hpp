#pragma once

#include <nlohmann/json.hpp>
#include <string>

struct User
{
    std::string id;
    std::string account;
    std::string name;
    std::string gender;
    std::string mobile;
    std::string email;
    std::string phone;
    std::string post;
    std::string type;
    bool enable = true;
    std::string dept_name;
    std::string team_name;
    std::string role_name;
};

void from_json(const nlohmann::json &j, User &p)
{
    j["id"].get_to(p.id);
    j["account"].get_to(p.account);
    j["name"].get_to(p.name);
    p.email = JsonUtil::get<std::string>(j, "email");
    p.gender = JsonUtil::get<std::string>(j, "gender");
    p.mobile = JsonUtil::get<std::string>(j, "mobile");
    p.phone = JsonUtil::get<std::string>(j, "phone");
    p.post = JsonUtil::get<std::string>(j, "post");
    p.type = JsonUtil::get<std::string>(j, "type");
    p.role_name = JsonUtil::get<std::string>(j, "roleName");
    p.team_name = JsonUtil::get<std::string>(j, "teamName");
    p.dept_name = JsonUtil::get<std::string>(j, "deptName");
    p.enable = JsonUtil::get<bool>(j, "enable");
}

void to_json(nlohmann::json &j, const User &p)
{
    j = nlohmann::json{
        { "id", p.id },
        { "account", p.account },
        { "name", p.name },
        { "gender", p.gender },
        { "mobile", p.mobile },
        { "email", p.email },
        { "phone", p.phone },
        { "post", p.post },
        { "type", p.type },
        { "dept_name", p.dept_name },
        { "team_name", p.team_name },
        { "role_name", p.role_name },
        { "enable", p.enable },
    };
}