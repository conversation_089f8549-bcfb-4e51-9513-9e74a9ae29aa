#pragma once

#include <fstream>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <string>

struct ServerConfig
{
    std::string host = "0.0.0.0";
    int port = 8080;
    int threads = 4;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ServerConfig, host, port, threads);

struct DatabaseConfig
{
    std::string path = "./data/dailyreport.db";
    int connection_pool_size = 10;
    bool enable_wal_mode = true;
    int busy_timeout = 30000;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DatabaseConfig, path, connection_pool_size, enable_wal_mode, busy_timeout);

struct BackendConfig
{
    std::string base_url = "http://***************:7000";
    int timeout = 30;
    int retry_count = 3;
    std::string refresh_token;
    std::string auth;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(BackendConfig, base_url, timeout, retry_count, refresh_token);

struct SyncConfig
{
    int interval_minutes = 60;
    bool auto_start = true;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SyncConfig, interval_minutes, auto_start);

struct HolidayApiTimeoutConfig
{
    int connection = 20;
    int read = 30;
    int write = 10;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(HolidayApiTimeoutConfig, connection, read, write);

struct HolidayApiConfig
{
    std::string source = { "https://github.do/raw.githubusercontent.com/NateScarlet/holiday-cn/master/{year}.json" };
    HolidayApiTimeoutConfig timeout;
    int retry_count = 3;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(HolidayApiConfig, source, timeout, retry_count);

struct Config
{
    ServerConfig server;
    DatabaseConfig database;
    BackendConfig backend;
    SyncConfig sync;
    HolidayApiConfig holiday;
};
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Config, server, database, backend, sync, holiday);
