# Authentication Configuration

## Overview

The Daily Report System requires authentication to access the backend API. This document explains how to configure authentication.

## Configuration

Edit the `config.json` file to configure authentication:

### Method 1: Using Authorization Header

```json
{
    "backend": {
        "base_url": "http://192.168.100.236:7000",
        "timeout": 30,
        "retry_count": 3,
        "auth": {
            "type": "header",
            "header_name": "Authorization",
            "header_value": "Bearer your_actual_token_here"
        }
    }
}
```

### Method 2: Using Token (Auto Bearer)

```json
{
    "backend": {
        "base_url": "http://192.168.100.236:7000",
        "timeout": 30,
        "retry_count": 3,
        "auth": {
            "type": "token",
            "token": "your_actual_token_here"
        }
    }
}
```

### Method 3: No Authentication

```json
{
    "backend": {
        "base_url": "http://192.168.100.236:7000",
        "timeout": 30,
        "retry_count": 3,
        "auth": {
            "type": "none"
        }
    }
}
```

## Getting Authentication Token

1. Contact your system administrator to get a valid authentication token
2. Or use the API login endpoint to obtain a token programmatically
3. Replace `your_actual_token_here` with the actual token value

## Troubleshooting

### 401 Authentication Error

If you see errors like:
```
[error] SyncService: User sync failed - Authentication required (401)
```

This means:
1. Your token is invalid or expired
2. Your token is not configured correctly
3. The API requires authentication but none is configured

**Solution:**
1. Check your `config.json` file
2. Ensure the token is valid and not expired
3. Contact your system administrator for a new token

### Chinese Character Encoding Issues

The system now includes proper UTF-8 encoding headers to handle Chinese characters correctly.

## Security Notes

1. Keep your authentication token secure
2. Do not commit tokens to version control
3. Rotate tokens regularly
4. Use environment variables for sensitive configuration in production
