#pragma once

#include "database/DatabaseManager.hpp"
#include "models/Project.hpp"
#include <optional>
#include <spdlog/spdlog.h>
#include <vector>

class ProjectDao
{
public:
    ProjectDao(DatabaseManager &db_manager)
        : db_manager_(db_manager)
    {
    }

    std::vector<Project> findAll()
    {
        std::vector<Project> projects;
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM projects ORDER BY id");

            while (query.executeStep())
            {
                Project project;
                project.id = query.getColumn(0).getInt();
                project.project_code = query.getColumn(1).getString();
                project.project_name = query.getColumn(2).getString();
                project.short_name = query.getColumn(3).getString();
                project.start_time = query.getColumn(4).getInt64();
                project.end_time = query.getColumn(5).getInt64();
                project.plan_start_time = query.getColumn(6).getInt64();
                project.plan_end_time = query.getColumn(7).getInt64();
                project.duty_persons = query.getColumn(8).getString();
                project.duty_departments = query.getColumn(9).getString();
                project.join_departments = query.getColumn(10).getString();
                project.join_persons = query.getColumn(11).getString();
                project.project_type = query.getColumn(12).getString();
                project.project_state = query.getColumn(13).getString();
                project.rel_product_list = query.getColumn(14).getString();
                project.rel_customer_info = query.getColumn(15).getInt();
                project.project_comment = query.getColumn(16).getString();
                project.contract_name = query.getColumn(17).getString();
                projects.push_back(project);
            }
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("ProjectDao::findAll error: {}", e.what());
        }
        return projects;
    }

    bool batchInsertOrUpdate(const std::vector<Project> &projects)
    {
        if (projects.empty())
        {
            return true;
        }

        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO projects (id, project_code, project_name, short_name, start_time, end_time,
                                               plan_start_time, plan_end_time, duty_persons, duty_departments,
                                               join_departments, join_persons, project_type, project_state,
                                               rel_product_list, rel_customer_info, project_comment, contract_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            )");

            int processed = 0;
            for (const auto &project : projects)
            {
                query.bind(1, project.id);
                query.bind(2, project.project_code);
                query.bind(3, project.project_name);
                query.bind(4, project.short_name);
                query.bind(5, project.start_time);
                query.bind(6, project.end_time);
                query.bind(7, project.plan_start_time);
                query.bind(8, project.plan_end_time);
                query.bind(9, project.duty_persons);
                query.bind(10, project.duty_departments);
                query.bind(11, project.join_departments);
                query.bind(12, project.join_persons);
                query.bind(13, project.project_type);
                query.bind(14, project.project_state);
                query.bind(15, project.rel_product_list);
                query.bind(16, project.rel_customer_info);
                query.bind(17, project.project_comment);
                query.bind(18, project.contract_name);

                processed++;
            }

            transaction.commit();
            SPDLOG_INFO("Batch operation completed - {}/{} records processed successfully", processed, projects.size());
            return processed == projects.size();
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("ProjectDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
    }

private:
    DatabaseManager &db_manager_;
};
