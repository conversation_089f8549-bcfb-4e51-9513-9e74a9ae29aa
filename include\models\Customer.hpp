#pragma once

#include "JsonUtil.hpp"
#include <string>

struct Customer
{
    std::string id;
    std::string contact;
    std::string department;
    std::string phones;
    std::string unit_name;
    std::string group_name;
};

void from_json(const nlohmann::json &j, Customer &p)
{
    j["id"].get_to(p.id);
    j["unitName"].get_to(p.unit_name);
    p.contact = JsonUtil::get<std::string>(j, "contact");
    p.department = JsonUtil::get<std::string>(j, "department");
    p.phones = JsonUtil::get<std::string>(j, "phones");
    p.group_name = JsonUtil::get<std::string>(j, "groupName");
}

void to_json(nlohmann::json &j, const Customer &p)
{
    j = nlohmann::json{
        { "id", p.id },
        { "contact", p.contact },
        { "department", p.department },
        { "phones", p.phones },
        { "unit_name", p.unit_name },
        { "group_name", p.group_name },
    };
}