#pragma once

#include "database/DatabaseManager.hpp"
#include "models/Product.hpp"
#include <optional>
#include <spdlog/spdlog.h>
#include <vector>

class ProductDao
{
public:
    ProductDao(DatabaseManager &db_manager)
        : db_manager_(db_manager)
    {
    }

    std::vector<Product> findAll()
    {
        std::vector<Product> products;
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM products ORDER BY id");

            while (query.executeStep())
            {
                Product product;
                product.id = query.getColumn(0).getInt();
                product.name = query.getColumn(1).getString();
                product.description = query.getColumn(2).getString();

                // Convert integer timestamps back to strings
                int64_t start_time = query.getColumn(3).getInt64();
                int64_t end_time = query.getColumn(4).getInt64();
                product.begin_time = start_time > 0 ? std::to_string(start_time) : "";
                product.end_time = end_time > 0 ? std::to_string(end_time) : "";

                product.group_name = query.getColumn(5).getString();
                products.push_back(product);
            }
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("ProductDao::findAll error: {}", e.what());
        }
        return products;
    }

    bool batchInsertOrUpdate(const std::vector<Product> &products)
    {
        if (products.empty())
        {
            return true;
        }

        int processed = 0;
        try
        {
            auto &db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO products (id, product_name, product_description, product_start_time, 
                                               product_end_time, group_name)
                VALUES (?, ?, ?, ?, ?, ?)
            )");

            for (const auto &product : products)
            {
                query.bind(1, product.id);
                query.bind(2, product.name);
                query.bind(3, product.description);
                query.bind(4, product.begin_time);
                query.bind(5, product.end_time);
                query.bind(6, product.group_name);
                processed ++;
            }

            transaction.commit();
            SPDLOG_INFO("Batch operation completed - {}/{} records processed successfully", processed, products.size());
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("ProductDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
            return processed == products.size();
    }

private:
    DatabaseManager &db_manager_;
};