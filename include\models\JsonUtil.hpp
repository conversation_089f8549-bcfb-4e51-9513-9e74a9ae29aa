﻿#pragma once

#include <nlohmann/json.hpp>

namespace JsonUtil
{

    template <typename T>
    T get(const nlohmann::json &item, const std::string &key, const T &default_value = T{})
    {
        if (item.contains(key) && !item[key].is_null())
        {
            try
            {
                return item[key].get<T>();
            }
            catch (const std::exception &e)
            {
                SPDLOG_WARN("Failed to parse field '{}': {}", key, e.what());
            }
        }
        return default_value;
    }

    template <typename T>
    nlohmann::json value(const nlohmann::json &item, const std::string &key, const T &default_value = T{})
    {
        if (item.contains(key) && !item[key].is_null())
        {
            return item[key];
        }
        return default_value;
    }
}  // namespace JsonUtil