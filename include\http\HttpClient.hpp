#pragma once

#include <chrono>
#include <fstream>
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <string>
#include <thread>

struct ApiResponse
{
    int code = -1;
    std::string msg;
    nlohmann::json data;

    operator bool() const
    {
        return code == 0 || code == 200;
    }
    NLOHMANN_DEFINE_TYPE_INTRUSIVE(ApiResponse, code, msg, data);
};

class HttpClient
{
public:
    HttpClient(BackendConfig config)
        : config_(std::move(config))
    {
        SPDLOG_INFO("Configuring HTTP client with base URL: {}", config_.base_url);
        refreshToken();
    }

    ApiResponse get(const std::string &path)
    {
        SPDLOG_INFO("GET {}", path);
        auto client = makeClient();
        auto result = client.Get(path);
        return makeResponse(result);
    }

    ApiResponse post(const std::string &path, const nlohmann::json &body)
    {
        SPDLOG_INFO("POST {}", path);
        auto client = makeClient();
        auto result = client.Post(path, body.dump(), "application/json; charset=utf-8");
        return makeResponse(result);
    }

private:
    bool refreshToken()
    {
        const auto &refresh_token = config_.refresh_token;
        const auto auth = config_.auth;
        SPDLOG_INFO("HttpClient: Starting token refresh...");

        std::string refreshUrl = "/atom/v1/atomRbacService/oauth/refreshToken?refreshToken=" + refresh_token;
        auto response = post(refreshUrl, {});
        if (!response)
        {
            SPDLOG_ERROR("HttpClient: Token refresh API call failed - Code: {}, Message: {}", response.code, response.msg);
            SPDLOG_ERROR("HttpClient: Response data: {}", response.data.dump());
            return false;
        }
        config_.refresh_token = response.data["refreshToken"];
        config_.auth = response.data["accessToken"];
        saveTokenToConfig();

        if (refresh_token != config_.refresh_token)
            SPDLOG_INFO("Refresh token changed {} -> {}", refresh_token, config_.refresh_token);
        if (auth != config_.auth)
            SPDLOG_INFO("Auth token changed {} -> {}", auth, config_.auth);

        return true;
    }

    // Save new token to config.json file
    bool saveTokenToConfig()
    {
        try
        {
            nlohmann::json config;
            {
                std::ifstream configFile("config.json");
                config = nlohmann::json::parse(configFile);
            }

            // Update the token
            config["backend"]["auth"] = config_.auth;
            config["backend"]["refresh_token"] = config_.refresh_token;

            // Write back to file
            {
                std::ofstream outFile("config.json");
                outFile << std::setw(4) << config << std::endl;
            }

            SPDLOG_INFO("HttpClient: Token saved to config.json successfully");
            return true;
        }
        catch (const std::exception &e)
        {
            SPDLOG_ERROR("HttpClient: Failed to save token to config: {}", e.what());
            return false;
        }
    }

    ApiResponse makeResponse(const httplib::Result &result) const
    {
        ApiResponse response;

        if (!result)
        {
            response.msg = "No response received";
            SPDLOG_ERROR("No response received");
            return response;
        }

        try
        {
            response = nlohmann::json::parse(result->body).get_to(response);
            SPDLOG_INFO("Response code={}, msg={}", response.code, response.msg);
        }
        catch (const std::exception &e)
        {
            response.msg = "Failed to parse JSON response: " + std::string(e.what());
            SPDLOG_ERROR(response.msg);
        }
        return response;
    }

    httplib::Client makeClient() const
    {
        httplib::Client client(config_.base_url);
        client.set_connection_timeout(config_.timeout);
        client.set_read_timeout(config_.timeout);
        client.set_compress(true);  // Enable gzip compression

        // Set headers for proper encoding and authentication
        httplib::Headers headers = {
            { "Accept", "application/json, text/plain, */*" },
            { "Accept-Encoding", "gzip, deflate" },
            { "Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7" },
            { "Accept-Charset", "utf-8" },
            { "Connection", "keep-alive" },
            { "Content-Type", "application/json; charset=utf-8" },
            { "User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" },
            { "Authorization", config_.auth },
        };
        client.set_default_headers(headers);
        return client;
    }

private:
    BackendConfig config_;
};
